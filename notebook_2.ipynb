# !pip install scipy
# !pip install scikit-learn

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

plt.style.use('dark_background')
RISK_COLORS = {'low': '#2ed573', 'medium': '#ffa502', 'high': '#ff4757', 'extreme': '#8b0000'}

fear_greed = pd.read_csv('csv_files/fear_greed_index (1).csv')
trading_data = pd.read_csv('csv_files/historical_data.csv')

fear_greed['date'] = pd.to_datetime(fear_greed['date'])
trading_data['date'] = pd.to_datetime(trading_data['Timestamp IST'].str.split(' ').str[0], format='%d-%m-%Y')

daily_risk_metrics = trading_data.groupby('date').agg({
    'Size USD': ['sum', 'std', 'count'],
    'Closed PnL': ['sum', 'std', 'skew'],
    'Account': 'nunique'
})

daily_risk_metrics.columns = ['total_volume', 'volume_volatility', 'trade_count', 
                             'total_pnl', 'pnl_volatility', 'pnl_skewness', 'unique_traders']
daily_risk_metrics = daily_risk_metrics.reset_index()

risk_data = pd.merge(fear_greed, daily_risk_metrics, on='date', how='inner')
risk_data['sharpe_proxy'] = risk_data['total_pnl'] / (risk_data['pnl_volatility'] + 1e-6)
risk_data['volume_concentration'] = risk_data['total_volume'] / risk_data['trade_count']
risk_data['trader_efficiency'] = risk_data['total_pnl'] / risk_data['unique_traders']

features_for_clustering = ['value', 'total_volume', 'pnl_volatility', 'sharpe_proxy', 'volume_concentration']
scaler = StandardScaler()
scaled_features = scaler.fit_transform(risk_data[features_for_clustering].fillna(0))

kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
risk_data['risk_regime'] = kmeans.fit_predict(scaled_features)

regime_mapping = {0: 'Low Risk', 1: 'Medium Risk', 2: 'High Risk', 3: 'Extreme Risk'}
risk_data['risk_label'] = risk_data['risk_regime'].map(regime_mapping)

print("Risk Regime Distribution:")
print(risk_data['risk_label'].value_counts())
print(f"\nAnalysis covers {len(risk_data)} trading days")

def calculate_var(returns, confidence=0.05):
    return np.percentile(returns, confidence * 100)

def calculate_cvar(returns, confidence=0.05):
    var = calculate_var(returns, confidence)
    return returns[returns <= var].mean()

risk_analysis = risk_data.groupby(['classification', 'risk_label']).agg({
    'total_pnl': ['mean', 'std', 'min', 'max'],
    'sharpe_proxy': 'mean',
    'volume_concentration': 'mean',
    'trader_efficiency': 'mean'
}).round(4)

risk_analysis.columns = ['avg_pnl', 'pnl_std', 'min_pnl', 'max_pnl', 
                        'avg_sharpe', 'avg_concentration', 'avg_efficiency']
risk_analysis = risk_analysis.reset_index()

var_95 = risk_data.groupby('classification')['total_pnl'].apply(lambda x: calculate_var(x, 0.05))
cvar_95 = risk_data.groupby('classification')['total_pnl'].apply(lambda x: calculate_cvar(x, 0.05))

risk_metrics_summary = pd.DataFrame({
    'VaR_95': var_95,
    'CVaR_95': cvar_95,
    'Max_Drawdown': risk_data.groupby('classification')['total_pnl'].min(),
    'Volatility': risk_data.groupby('classification')['total_pnl'].std()
})

fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Portfolio Risk Metrics Dashboard', fontsize=16, y=0.98)

sentiment_colors = {'Extreme Fear': '#8b0000', 'Fear': '#ff4757', 'Neutral': '#ffa502', 
                   'Greed': '#2ed573', 'Extreme Greed': '#006400'}

risk_metrics_summary['VaR_95'].plot(kind='bar', ax=axes[0,0], 
                                   color=[sentiment_colors.get(x, '#95a5a6') for x in risk_metrics_summary.index])
axes[0,0].set_title('Value at Risk (95%)')
axes[0,0].tick_params(axis='x', rotation=45)

risk_metrics_summary['CVaR_95'].plot(kind='bar', ax=axes[0,1],
                                    color=[sentiment_colors.get(x, '#95a5a6') for x in risk_metrics_summary.index])
axes[0,1].set_title('Conditional VaR (95%)')
axes[0,1].tick_params(axis='x', rotation=45)

risk_metrics_summary['Volatility'].plot(kind='bar', ax=axes[1,0],
                                        color=[sentiment_colors.get(x, '#95a5a6') for x in risk_metrics_summary.index])
axes[1,0].set_title('PnL Volatility')
axes[1,0].tick_params(axis='x', rotation=45)

axes[1,1].scatter(risk_data['pnl_volatility'], risk_data['total_pnl'], 
                 c=[sentiment_colors.get(x, '#95a5a6') for x in risk_data['classification']], alpha=0.6)
axes[1,1].set_title('Risk-Return Scatter')
axes[1,1].set_xlabel('PnL Volatility')
axes[1,1].set_ylabel('Total PnL')

plt.tight_layout()
plt.show()

print("\n📊 Risk Metrics Summary:")
print(risk_metrics_summary.round(2))

from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
import pandas as pd

# Extract and transform features
ml_features = risk_data[['value', 'total_volume', 'pnl_volatility', 'sharpe_proxy', 
                         'volume_concentration', 'trader_efficiency']].fillna(0)

pca = PCA(n_components=2)
pca_features = pca.fit_transform(StandardScaler().fit_transform(ml_features))

risk_data['pca_1'] = pca_features[:, 0]
risk_data['pca_2'] = pca_features[:, 1]

# Prepare figure with adjusted spacing and layout
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'PCA Clustering by Sentiment',
        'Risk Regime Evolution',
        'Feature Importance',
        'Regime Transition Matrix'
    ),
    specs=[[{"type": "scatter"}, {"type": "scatter"}],
           [{"type": "bar"}, {"type": "heatmap"}]],
    vertical_spacing=0.15,
    horizontal_spacing=0.1
)

# PCA Scatter
for sentiment in risk_data['classification'].unique():
    subset = risk_data[risk_data['classification'] == sentiment]
    fig.add_trace(
        go.Scatter(
            x=subset['pca_1'], y=subset['pca_2'],
            mode='markers', name=sentiment,
            marker=dict(size=7, opacity=0.75)
        ),
        row=1, col=1
    )

# Risk Regime Line
fig.add_trace(
    go.Scatter(
        x=risk_data['date'], y=risk_data['risk_regime'],
        mode='lines+markers', name='Risk Regime',
        line=dict(width=2)
    ),
    row=1, col=2
)

# Feature Importance Bar
feature_importance = pd.Series(
    pca.components_[0], index=ml_features.columns
).abs().sort_values(ascending=False)

fig.add_trace(
    go.Bar(
        x=feature_importance.values, y=feature_importance.index,
        orientation='h', name='Importance',
        marker=dict(color='skyblue')
    ),
    row=2, col=1
)

# Regime Transition Matrix Heatmap
risk_data['next_regime'] = risk_data['risk_regime'].shift(-1)
transition_matrix = pd.crosstab(
    risk_data['risk_regime'], risk_data['next_regime'], normalize='index'
)

fig.add_trace(
    go.Heatmap(
        z=transition_matrix.values,
        x=[f'To {regime_mapping[i]}' for i in transition_matrix.columns],
        y=[f'From {regime_mapping[i]}' for i in transition_matrix.index],
        colorscale='RdYlBu_r',
        colorbar=dict(title='Transition Prob.')
    ),
    row=2, col=2
)

# Final layout adjustments
fig.update_layout(
    height=900,
    width=1100,
    title_text="📊 Advanced Pattern Recognition Analysis",
    title_x=0.5,
    template='plotly_dark',
    margin=dict(t=80, b=40, l=60, r=60),
    font=dict(size=12),
    legend=dict(
        orientation="h",
        yanchor="bottom",
        y=1.02,
        xanchor="right",
        x=1
    )
)

fig.show()

# Print insights
print("\n ML Insights:")
print(f"PCA Explained Variance: {pca.explained_variance_ratio_.sum():.3f}")
print(f"Most important feature: {feature_importance.index[0]}")
print(f"Regime persistence: {np.diag(transition_matrix).mean():.3f}")


risk_data['sentiment_momentum'] = risk_data['value'].rolling(3).mean() - risk_data['value'].rolling(7).mean()
risk_data['volume_momentum'] = risk_data['total_volume'].pct_change()
risk_data['volatility_regime'] = pd.qcut(risk_data['pnl_volatility'], 4, labels=['Low', 'Medium', 'High', 'Extreme'])

def generate_advanced_signals(row):
    if row['classification'] == 'Extreme Fear' and row['sentiment_momentum'] > 0 and row['volatility_regime'] == 'High':
        return 'Strong Buy'
    elif row['classification'] == 'Extreme Greed' and row['sentiment_momentum'] < 0 and row['volume_momentum'] < -0.1:
        return 'Strong Sell'
    elif row['risk_label'] == 'Low Risk' and row['sharpe_proxy'] > 0.5:
        return 'Accumulate'
    elif row['risk_label'] == 'Extreme Risk':
        return 'Reduce Position'
    else:
        return 'Hold'

risk_data['advanced_signal'] = risk_data.apply(generate_advanced_signals, axis=1)

signal_performance = risk_data.groupby('advanced_signal').agg({
    'total_pnl': ['mean', 'std', 'count'],
    'sharpe_proxy': 'mean',
    'trader_efficiency': 'mean'
}).round(4)

signal_performance.columns = ['avg_pnl', 'pnl_std', 'occurrences', 'avg_sharpe', 'avg_efficiency']
signal_performance['risk_adjusted_return'] = signal_performance['avg_pnl'] / signal_performance['pnl_std']

fig, axes = plt.subplots(2, 2, figsize=(16, 10))
fig.suptitle('Advanced Signal Performance Analysis', fontsize=16, y=0.98)

signal_colors = {'Strong Buy': '#006400', 'Buy': '#2ed573', 'Accumulate': '#3742fa',
                'Hold': '#95a5a6', 'Reduce Position': '#ffa502', 'Strong Sell': '#ff4757'}

signal_performance['avg_pnl'].plot(kind='bar', ax=axes[0,0],
                                  color=[signal_colors.get(x, '#95a5a6') for x in signal_performance.index])
axes[0,0].set_title('Average PnL by Signal')
axes[0,0].tick_params(axis='x', rotation=45)

signal_performance['risk_adjusted_return'].plot(kind='bar', ax=axes[0,1],
                                               color=[signal_colors.get(x, '#95a5a6') for x in signal_performance.index])
axes[0,1].set_title('Risk-Adjusted Returns')
axes[0,1].tick_params(axis='x', rotation=45)

signal_performance['occurrences'].plot(kind='pie', ax=axes[1,0], autopct='%1.1f%%',
                                      colors=[signal_colors.get(x, '#95a5a6') for x in signal_performance.index])
axes[1,0].set_title('Signal Distribution')

axes[1,1].scatter(signal_performance['pnl_std'], signal_performance['avg_pnl'],
                 c=[signal_colors.get(x, '#95a5a6') for x in signal_performance.index], s=100, alpha=0.8)
for i, signal in enumerate(signal_performance.index):
    axes[1,1].annotate(signal, (signal_performance['pnl_std'].iloc[i], signal_performance['avg_pnl'].iloc[i]),
                      xytext=(5, 5), textcoords='offset points', fontsize=8)
axes[1,1].set_title('Risk vs Return by Signal')
axes[1,1].set_xlabel('PnL Standard Deviation')
axes[1,1].set_ylabel('Average PnL')

plt.tight_layout()
plt.show()

print("\n🎯 Advanced Signal Performance:")
print(signal_performance.sort_values('risk_adjusted_return', ascending=False))

best_signal = signal_performance['risk_adjusted_return'].idxmax()
print(f"\n🏆 Best risk-adjusted signal: {best_signal}")
print(f"Risk-adjusted return: {signal_performance.loc[best_signal, 'risk_adjusted_return']:.3f}")

